/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useRef } from "react";
import classNames from "classnames";
import { toast } from "sonner";
import { useLocalStorage, useUpdateEffect } from "react-use";
import { ArrowUp, ChevronDown, Sparkles, X, Check } from "lucide-react";
import { FaStopCircle } from "react-icons/fa";

import Login from "../login/login";
import { defaultHTML } from "../../../utils/consts";
import SuccessSound from "./../../assets/success.mp3";
import Settings from "../settings/settings";
import ProModal from "../pro-modal/pro-modal";
import { Button } from "../ui/button";
// @ts-expect-error not needed
import { MODELS } from "../../../utils/providers";
import Loading from "../loading/loading";
import { HtmlHistory } from "../../../utils/types";
import InviteFriends from "../invite-friends/invite-friends";
import ReImagine from "../re-imagine/re-imagine";
import <PERSON><PERSON><PERSON>eyManager from "../../utils/api-keys";

function AskAI({
  html,
  setHtml,
  onScrollToBottom,
  isAiWorking,
  setisAiWorking,
  onNewPrompt,
  onSuccess,
}: {
  html: string;
  setHtml: (html: string) => void;
  onScrollToBottom: () => void;
  isAiWorking: boolean;
  onNewPrompt: (prompt: string) => void;
  htmlHistory?: HtmlHistory[];
  setisAiWorking: React.Dispatch<React.SetStateAction<boolean>>;
  onSuccess: (h: string, p: string, n?: number[][]) => void;
}) {
  const refThink = useRef<HTMLDivElement | null>(null);

  const [open, setOpen] = useState(false);
  const [prompt, setPrompt] = useState("");
  const [hasAsked, setHasAsked] = useState(false);
  const [previousPrompt, setPreviousPrompt] = useState("");
  const [provider, setProvider] = useLocalStorage("provider", "auto");
  const [model, setModel] = useLocalStorage("model", MODELS[0].value);
  const [openProvider, setOpenProvider] = useState(false);
  const [providerError, setProviderError] = useState("");
  const [openProModal, setOpenProModal] = useState(false);
  const [think, setThink] = useState<string | undefined>(undefined);
  const [openThink, setOpenThink] = useState(false);
  const [isThinking, setIsThinking] = useState(true);
  const [controller, setController] = useState<AbortController | null>(null);
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [enhancedPrompt, setEnhancedPrompt] = useState("");
  const [showEnhancement, setShowEnhancement] = useState(false);

  const audio = new Audio(SuccessSound);
  audio.volume = 0.5;

  const callAi = async (redesignMarkdown?: string) => {
    if (isAiWorking) return;
    if (!redesignMarkdown && !prompt.trim()) return;
    setisAiWorking(true);
    setProviderError("");
    setThink("");
    setOpenThink(false);
    setIsThinking(true);

    let contentResponse = "";
    let thinkResponse = "";
    let lastRenderTime = 0;

    const isFollowUp = html !== defaultHTML;
    const abortController = new AbortController();
    setController(abortController);

    // Get API keys for external providers
    const apiKeys = ApiKeyManager.getApiKeys();

    try {
      onNewPrompt(prompt);
      if (isFollowUp && !redesignMarkdown) {
        const headers = {
          "Content-Type": "application/json",
          ...(apiKeys.groq && { "x-groq-api-key": apiKeys.groq }),
          ...(apiKeys.openrouter && { "x-openrouter-api-key": apiKeys.openrouter }),
          ...(apiKeys.google && { "x-google-api-key": apiKeys.google }),
        };

        const request = await fetch("/api/ask-ai", {
          method: "PUT",
          body: JSON.stringify({
            prompt,
            provider,
            previousPrompt,
            html,
          }),
          headers,
          signal: abortController.signal,
        });
        if (request && request.body) {
          const res = await request.json();
          if (!request.ok) {
            if (res.openLogin) {
              setOpen(true);
            } else if (res.openSelectProvider) {
              setOpenProvider(true);
              setProviderError(res.message);
            } else if (res.openProModal) {
              setOpenProModal(true);
            } else {
              toast.error(res.message);
            }
            setisAiWorking(false);
            return;
          }
          setHtml(res.html);
          toast.success("AI responded successfully");
          setPreviousPrompt(prompt);
          setPrompt("");
          setisAiWorking(false);
          onSuccess(res.html, prompt, res.updatedLines);
          audio.play();
        }
      } else {
        const headers = {
          "Content-Type": "application/json",
          ...(apiKeys.groq && { "x-groq-api-key": apiKeys.groq }),
          ...(apiKeys.openrouter && { "x-openrouter-api-key": apiKeys.openrouter }),
          ...(apiKeys.google && { "x-google-api-key": apiKeys.google }),
        };

        const request = await fetch("/api/ask-ai", {
          method: "POST",
          body: JSON.stringify({
            prompt,
            provider,
            model,
            redesignMarkdown,
          }),
          headers,
          signal: abortController.signal,
        });
        if (request && request.body) {
          if (!request.ok) {
            const res = await request.json();
            if (res.openLogin) {
              setOpen(true);
            } else if (res.openSelectProvider) {
              setOpenProvider(true);
              setProviderError(res.message);
            } else if (res.openProModal) {
              setOpenProModal(true);
            } else {
              toast.error(res.message);
            }
            setisAiWorking(false);
            return;
          }
          const reader = request.body.getReader();
          const decoder = new TextDecoder("utf-8");
          const selectedModel = MODELS.find(
            (m: { value: string }) => m.value === model
          );
          let contentThink: string | undefined = undefined;
          const read = async () => {
            const { done, value } = await reader.read();
            if (done) {
              toast.success("AI responded successfully");
              setPreviousPrompt(prompt);
              setPrompt("");
              setisAiWorking(false);
              setHasAsked(true);
              audio.play();

              // Now we have the complete HTML including </html>, so set it to be sure
              const finalDoc = contentResponse.match(
                /<!DOCTYPE html>[\s\S]*<\/html>/
              )?.[0];
              if (finalDoc) {
                setHtml(finalDoc);
              }
              onSuccess(finalDoc ?? contentResponse, prompt);

              return;
            }

            const chunk = decoder.decode(value, { stream: true });
            thinkResponse += chunk;
            if (selectedModel?.isThinker) {
              const thinkMatch = thinkResponse.match(/<think>[\s\S]*/)?.[0];
              if (thinkMatch && !thinkResponse?.includes("</think>")) {
                if ((contentThink?.length ?? 0) < 3) {
                  setOpenThink(true);
                }
                setThink(thinkMatch.replace("<think>", "").trim());
                contentThink += chunk;
                return read();
              }
            }

            contentResponse += chunk;

            const newHtml = contentResponse.match(
              /<!DOCTYPE html>[\s\S]*/
            )?.[0];
            if (newHtml) {
              setIsThinking(false);
              let partialDoc = newHtml;
              if (
                partialDoc.includes("<head>") &&
                !partialDoc.includes("</head>")
              ) {
                partialDoc += "\n</head>";
              }
              if (
                partialDoc.includes("<body") &&
                !partialDoc.includes("</body>")
              ) {
                partialDoc += "\n</body>";
              }
              if (!partialDoc.includes("</html>")) {
                partialDoc += "\n</html>";
              }

              // Throttle the re-renders to avoid flashing/flicker
              const now = Date.now();
              if (now - lastRenderTime > 300) {
                setHtml(partialDoc);
                lastRenderTime = now;
              }

              if (partialDoc.length > 200) {
                onScrollToBottom();
              }
            }
            read();
          };

          read();
        }
      }
    } catch (error: any) {
      setisAiWorking(false);
      toast.error(error.message);
      if (error.openLogin) {
        setOpen(true);
      }
    }
  };

  const stopController = () => {
    if (controller) {
      controller.abort();
      setController(null);
      setisAiWorking(false);
      setThink("");
      setOpenThink(false);
      setIsThinking(false);
    }
  };

  const enhancePrompt = async () => {
    if (!prompt.trim() || isEnhancing || isAiWorking) return;

    setIsEnhancing(true);
    try {
      const response = await fetch("/api/enhance-prompt", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ prompt: prompt.trim() }),
      });

      if (!response.ok) {
        const error = await response.json();
        toast.error(error.message || "Failed to enhance prompt");
        return;
      }

      const data = await response.json();
      setEnhancedPrompt(data.enhancedPrompt);
      setShowEnhancement(true);
    } catch (error: any) {
      toast.error(error.message || "Failed to enhance prompt");
    } finally {
      setIsEnhancing(false);
    }
  };

  const acceptEnhancement = () => {
    setPrompt(enhancedPrompt);
    setShowEnhancement(false);
    setEnhancedPrompt("");
  };

  const rejectEnhancement = () => {
    setShowEnhancement(false);
    setEnhancedPrompt("");
  };

  useUpdateEffect(() => {
    if (refThink.current) {
      refThink.current.scrollTop = refThink.current.scrollHeight;
    }
  }, [think]);

  useUpdateEffect(() => {
    if (!isThinking) {
      setOpenThink(false);
    }
  }, [isThinking]);

  return (
    <div className="bg-neutral-800 border border-neutral-700 rounded-2xl ring-[4px] focus-within:ring-neutral-500/30 focus-within:border-neutral-600 ring-transparent z-10 absolute bottom-3 left-3 w-[calc(100%-20px)] group">
      {think && (
        <div className="w-full border-b border-neutral-700 relative overflow-hidden">
          <header
            className="flex items-center justify-between px-5 py-2.5 group hover:bg-neutral-600/20 transition-colors duration-200 cursor-pointer"
            onClick={() => {
              setOpenThink(!openThink);
            }}
          >
            <p className="text-sm font-medium text-neutral-300 group-hover:text-neutral-200 transition-colors duration-200">
              {isThinking ? "DeepSite is thinking..." : "DeepSite's plan"}
            </p>
            <ChevronDown
              className={classNames(
                "size-4 text-neutral-400 group-hover:text-neutral-300 transition-all duration-200",
                {
                  "rotate-180": openThink,
                }
              )}
            />
          </header>
          <main
            ref={refThink}
            className={classNames(
              "overflow-y-auto transition-all duration-200 ease-in-out",
              {
                "max-h-[0px]": !openThink,
                "min-h-[250px] max-h-[250px] border-t border-neutral-700":
                  openThink,
              }
            )}
          >
            <p className="text-[13px] text-neutral-400 whitespace-pre-line px-5 pb-4 pt-3">
              {think}
            </p>
          </main>
        </div>
      )}
      <div className="w-full relative flex items-center justify-between">
        {isAiWorking && (
          <div className="absolute bg-neutral-800 rounded-lg bottom-0 left-4 w-[calc(100%-30px)] h-full z-1 flex items-center justify-between max-lg:text-sm">
            <div className="flex items-center justify-start gap-2">
              <Loading overlay={false} className="!size-4" />
              <p className="text-neutral-400 text-sm">
                AI is {isThinking ? "thinking" : "coding"}...{" "}
              </p>
            </div>
            <div
              className="text-xs text-neutral-400 px-1 py-0.5 rounded-md border border-neutral-600 flex items-center justify-center gap-1.5 bg-neutral-800 hover:brightness-110 transition-all duration-200 cursor-pointer"
              onClick={stopController}
            >
              <FaStopCircle />
              Stop generation
            </div>
          </div>
        )}
        <input
          type="text"
          disabled={isAiWorking}
          className="w-full bg-transparent text-sm outline-none text-white placeholder:text-neutral-400 p-4"
          placeholder={
            hasAsked ? "Ask DeepSite for edits" : "Ask DeepSite anything..."
          }
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === "Enter" && !e.shiftKey) {
              callAi();
            }
          }}
        />
      </div>
      <div className="flex items-center justify-between gap-2 px-4 pb-3">
        <div className="flex-1 flex items-center justify-start gap-1.5">
          <ReImagine onRedesign={(md) => callAi(md)} />
          <InviteFriends />
        </div>
        <div className="flex items-center justify-end gap-2">
          <Settings
            provider={provider as string}
            model={model as string}
            onChange={setProvider}
            onModelChange={setModel}
            open={openProvider}
            error={providerError}
            onClose={setOpenProvider}
          />
          <Button
            size="iconXs"
            variant="ghost"
            disabled={isAiWorking || !prompt.trim() || isEnhancing}
            onClick={enhancePrompt}
            title="Enhance prompt"
          >
            {isEnhancing ? (
              <Loading overlay={false} className="!size-4" />
            ) : (
              <Sparkles className="size-4" />
            )}
          </Button>
          <Button
            size="iconXs"
            disabled={isAiWorking || !prompt.trim()}
            onClick={() => callAi()}
          >
            <ArrowUp className="size-4" />
          </Button>
        </div>
      </div>
      <div
        className={classNames(
          "h-screen w-screen bg-black/20 fixed left-0 top-0 z-10",
          {
            "opacity-0 pointer-events-none": !open,
          }
        )}
        onClick={() => setOpen(false)}
      ></div>
      <div
        className={classNames(
          "absolute top-0 -translate-y-[calc(100%+8px)] right-0 z-10 w-80 border border-neutral-800 !bg-neutral-900 rounded-lg shadow-lg transition-all duration-75 overflow-hidden",
          {
            "opacity-0 pointer-events-none": !open,
          }
        )}
      >
        <Login html={html}>
          <p className="text-gray-500 text-sm mb-3">
            You reached the limit of free AI usage. Please login to continue.
          </p>
        </Login>
      </div>
      <ProModal
        html={html}
        open={openProModal}
        onClose={() => setOpenProModal(false)}
      />

      {/* Enhancement Modal */}
      {showEnhancement && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-neutral-900 border border-neutral-700 rounded-2xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
            <header className="flex items-center justify-between p-4 border-b border-neutral-700">
              <div className="flex items-center gap-2">
                <Sparkles className="size-5 text-blue-400" />
                <h3 className="text-lg font-semibold text-white">Enhanced Prompt</h3>
              </div>
              <Button
                size="iconXs"
                variant="ghost"
                onClick={rejectEnhancement}
              >
                <X className="size-4" />
              </Button>
            </header>

            <div className="p-4 space-y-4">
              <div>
                <p className="text-sm text-neutral-400 mb-2">Original prompt:</p>
                <div className="bg-neutral-800 rounded-lg p-3 text-sm text-neutral-300">
                  {prompt}
                </div>
              </div>

              <div>
                <p className="text-sm text-neutral-400 mb-2">Enhanced prompt:</p>
                <div className="bg-neutral-800 rounded-lg p-3 text-sm text-white">
                  {enhancedPrompt}
                </div>
              </div>
            </div>

            <div className="flex items-center justify-end gap-2 p-4 border-t border-neutral-700">
              <Button
                variant="ghost"
                size="sm"
                onClick={rejectEnhancement}
              >
                Keep Original
              </Button>
              <Button
                size="sm"
                onClick={acceptEnhancement}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Check className="size-4 mr-1" />
                Use Enhanced
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default AskAI;
