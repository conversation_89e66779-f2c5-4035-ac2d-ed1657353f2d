import { SavedFile, FileManagerState, HtmlHistory } from "../../utils/types";
import { defaultHTML } from "../../utils/consts";

const FILE_STORAGE_KEY = "deepsite_files";
const CURRENT_FILE_KEY = "deepsite_current_file";

export class FileManager {
  private static instance: FileManager;
  
  static getInstance(): FileManager {
    if (!FileManager.instance) {
      FileManager.instance = new FileManager();
    }
    return FileManager.instance;
  }

  private constructor() {}

  // Generate unique ID for files
  private generateId(): string {
    return `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Get all files from localStorage
  getFiles(): SavedFile[] {
    try {
      const stored = localStorage.getItem(FILE_STORAGE_KEY);
      if (!stored) return [];
      
      const files = JSON.parse(stored);
      return files.map((file: any) => ({
        ...file,
        createdAt: new Date(file.createdAt),
        modifiedAt: new Date(file.modifiedAt),
        history: file.history.map((h: any) => ({
          ...h,
          createdAt: new Date(h.createdAt)
        }))
      }));
    } catch (error) {
      console.error("Error loading files:", error);
      return [];
    }
  }

  // Save files to localStorage
  private saveFiles(files: SavedFile[]): void {
    try {
      localStorage.setItem(FILE_STORAGE_KEY, JSON.stringify(files));
    } catch (error) {
      console.error("Error saving files:", error);
      throw new Error("Failed to save files. Storage might be full.");
    }
  }

  // Get current file ID
  getCurrentFileId(): string | null {
    return localStorage.getItem(CURRENT_FILE_KEY);
  }

  // Set current file ID
  setCurrentFileId(fileId: string | null): void {
    if (fileId) {
      localStorage.setItem(CURRENT_FILE_KEY, fileId);
    } else {
      localStorage.removeItem(CURRENT_FILE_KEY);
    }
  }

  // Create a new file
  createFile(name: string, html: string = defaultHTML): SavedFile {
    const files = this.getFiles();
    
    // Check if file name already exists
    const existingFile = files.find(f => f.name.toLowerCase() === name.toLowerCase());
    if (existingFile) {
      throw new Error(`A file named "${name}" already exists`);
    }

    const newFile: SavedFile = {
      id: this.generateId(),
      name,
      html,
      createdAt: new Date(),
      modifiedAt: new Date(),
      history: []
    };

    files.push(newFile);
    this.saveFiles(files);
    
    return newFile;
  }

  // Get a specific file by ID
  getFile(fileId: string): SavedFile | null {
    const files = this.getFiles();
    return files.find(f => f.id === fileId) || null;
  }

  // Update file content
  updateFile(fileId: string, html: string, prompt?: string): void {
    const files = this.getFiles();
    const fileIndex = files.findIndex(f => f.id === fileId);
    
    if (fileIndex === -1) {
      throw new Error("File not found");
    }

    const file = files[fileIndex];
    
    // Add to history if content changed
    if (file.html !== html && prompt) {
      file.history.push({
        html: file.html,
        createdAt: new Date(),
        prompt
      });
    }

    file.html = html;
    file.modifiedAt = new Date();
    
    this.saveFiles(files);
  }

  // Rename a file
  renameFile(fileId: string, newName: string): void {
    const files = this.getFiles();
    const fileIndex = files.findIndex(f => f.id === fileId);
    
    if (fileIndex === -1) {
      throw new Error("File not found");
    }

    // Check if new name already exists (excluding current file)
    const existingFile = files.find(f => f.id !== fileId && f.name.toLowerCase() === newName.toLowerCase());
    if (existingFile) {
      throw new Error(`A file named "${newName}" already exists`);
    }

    files[fileIndex].name = newName;
    files[fileIndex].modifiedAt = new Date();
    
    this.saveFiles(files);
  }

  // Delete a file
  deleteFile(fileId: string): void {
    const files = this.getFiles();
    const filteredFiles = files.filter(f => f.id !== fileId);
    
    if (filteredFiles.length === files.length) {
      throw new Error("File not found");
    }

    this.saveFiles(filteredFiles);
    
    // Clear current file if it was deleted
    if (this.getCurrentFileId() === fileId) {
      this.setCurrentFileId(null);
    }
  }

  // Export file as HTML
  exportFile(fileId: string): void {
    const file = this.getFile(fileId);
    if (!file) {
      throw new Error("File not found");
    }

    const blob = new Blob([file.html], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = file.name;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    
    URL.revokeObjectURL(url);
  }

  // Import file from File object
  async importFile(file: File): Promise<SavedFile> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const html = e.target?.result as string;
          const fileName = file.name.endsWith('.html') ? file.name : `${file.name}.html`;
          
          const savedFile = this.createFile(fileName, html);
          resolve(savedFile);
        } catch (error) {
          reject(error);
        }
      };
      
      reader.onerror = () => {
        reject(new Error("Failed to read file"));
      };
      
      reader.readAsText(file);
    });
  }

  // Migrate legacy localStorage data
  migrateLegacyData(): void {
    const legacyHtml = localStorage.getItem("html_content");
    if (legacyHtml && this.getFiles().length === 0) {
      try {
        const defaultFile = this.createFile("Untitled.html", legacyHtml);
        this.setCurrentFileId(defaultFile.id);
        
        // Remove legacy storage
        localStorage.removeItem("html_content");
        
        console.log("Migrated legacy HTML content to file system");
      } catch (error) {
        console.error("Failed to migrate legacy data:", error);
      }
    }
  }

  // Get file manager state
  getState(): FileManagerState {
    return {
      files: this.getFiles(),
      currentFileId: this.getCurrentFileId()
    };
  }

  // Clear all files (for reset functionality)
  clearAllFiles(): void {
    localStorage.removeItem(FILE_STORAGE_KEY);
    localStorage.removeItem(CURRENT_FILE_KEY);
  }
}

export default FileManager.getInstance();
