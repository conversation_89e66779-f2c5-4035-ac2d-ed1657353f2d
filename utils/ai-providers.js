// AI Provider integrations for external APIs
import { InferenceClient } from "@huggingface/inference";

// Groq API integration
export async function callGroqAPI(apiKey, model, messages, maxTokens, isStreaming = false) {
  const response = await fetch("https://api.groq.com/openai/v1/chat/completions", {
    method: "POST",
    headers: {
      "Authorization": `Bear<PERSON> ${apiKey}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      model: model,
      messages: messages,
      max_tokens: maxTokens,
      stream: isStreaming,
      temperature: 0.7,
    }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error?.message || "Groq API request failed");
  }

  return response;
}

// OpenRouter API integration
export async function callOpenRouterAPI(apiKey, model, messages, maxTokens, isStreaming = false) {
  const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
    method: "POST",
    headers: {
      "Authorization": `Bearer ${apiKey}`,
      "Content-Type": "application/json",
      "HTTP-Referer": "https://deepsite.ai", // Required by OpenRouter
      "X-Title": "DeepSite", // Optional, for tracking
    },
    body: JSON.stringify({
      model: model,
      messages: messages,
      max_tokens: maxTokens,
      stream: isStreaming,
      temperature: 0.7,
    }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error?.message || "OpenRouter API request failed");
  }

  return response;
}

// Google Gemini API integration
export async function callGoogleAPI(apiKey, model, messages, maxTokens, isStreaming = false) {
  // Convert OpenAI format messages to Google format
  const googleMessages = messages.map(msg => {
    if (msg.role === "system") {
      return {
        role: "user",
        parts: [{ text: `System: ${msg.content}` }]
      };
    }
    return {
      role: msg.role === "assistant" ? "model" : "user",
      parts: [{ text: msg.content }]
    };
  });

  // Combine system and user messages for Google's format
  const combinedContent = messages.map(msg => {
    if (msg.role === "system") return `System: ${msg.content}`;
    return msg.content;
  }).join("\n\n");

  const requestBody = {
    contents: [{
      role: "user",
      parts: [{ text: combinedContent }]
    }],
    generationConfig: {
      maxOutputTokens: maxTokens,
      temperature: 0.7,
    },
  };

  const endpoint = isStreaming 
    ? `https://generativelanguage.googleapis.com/v1beta/models/${model}:streamGenerateContent?key=${apiKey}`
    : `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${apiKey}`;

  const response = await fetch(endpoint, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(requestBody),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error?.message || "Google API request failed");
  }

  return response;
}

// Unified streaming handler for external providers
export async function handleExternalProviderStream(response, res, provider, model) {
  if (provider === "google") {
    // Handle Google's streaming format
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let completeResponse = "";

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              const content = data.candidates?.[0]?.content?.parts?.[0]?.text;
              if (content) {
                completeResponse += content;
                res.write(content);
                
                if (completeResponse.includes("</html>")) {
                  return;
                }
              }
            } catch (e) {
              // Skip invalid JSON lines
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  } else {
    // Handle OpenAI-compatible streaming (Groq, OpenRouter)
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let completeResponse = "";

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') return;
            
            try {
              const parsed = JSON.parse(data);
              const content = parsed.choices?.[0]?.delta?.content;
              if (content) {
                completeResponse += content;
                res.write(content);
                
                if (completeResponse.includes("</html>")) {
                  return;
                }
              }
            } catch (e) {
              // Skip invalid JSON lines
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }
}

// Non-streaming handler for external providers
export async function handleExternalProviderResponse(response, provider) {
  const data = await response.json();
  
  if (provider === "google") {
    return data.candidates?.[0]?.content?.parts?.[0]?.text || "";
  } else {
    // OpenAI-compatible format (Groq, OpenRouter)
    return data.choices?.[0]?.message?.content || "";
  }
}

// Get API key from request headers or body
export function getApiKeyFromRequest(req, provider) {
  // Check headers first (for security)
  const headerKey = req.headers[`x-${provider}-api-key`];
  if (headerKey) return headerKey;
  
  // Check body as fallback
  const bodyKey = req.body[`${provider}ApiKey`];
  if (bodyKey) return bodyKey;
  
  return null;
}
