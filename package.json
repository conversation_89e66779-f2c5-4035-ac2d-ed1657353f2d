{"name": "html-space-editor", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "start": "node server.js"}, "dependencies": {"@huggingface/hub": "^1.1.1", "@huggingface/inference": "^4.0.2", "@monaco-editor/react": "^4.7.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/vite": "^4.0.15", "@xenova/transformers": "^2.17.2", "body-parser": "^1.20.3", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "cookie-parser": "^1.4.7", "dotenv": "^16.4.7", "express": "^4.21.2", "lucide-react": "^0.511.0", "monaco-editor": "^0.52.2", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-speech-recognition": "^4.0.0", "react-toastify": "^11.0.5", "react-use": "^17.6.0", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.0.15"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/express": "^5.0.1", "@types/node": "^22.15.21", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/react-speech-recognition": "^3.9.6", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "tw-animate-css": "^1.3.0", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}, "packageManager": "yarn@4.9.1+sha512.f95ce356460e05be48d66401c1ae64ef84d163dd689964962c6888a9810865e39097a5e9de748876c2e0bf89b232d583c33982773e9903ae7a76257270986538"}