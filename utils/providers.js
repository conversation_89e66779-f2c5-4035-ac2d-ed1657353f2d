export const PROVIDERS = {
  "fireworks-ai": {
    name: "Fireworks AI",
    max_tokens: 131_000,
    id: "fireworks-ai",
  },
  nebius: {
    name: "Nebius AI Studio",
    max_tokens: 131_000,
    id: "nebius",
  },
  sambanova: {
    name: "<PERSON><PERSON><PERSON><PERSON>",
    max_tokens: 32_000,
    id: "sambanova",
  },
  novita: {
    name: "NovitaAI",
    max_tokens: 16_000,
    id: "novita",
  },
  hyperbolic: {
    name: "Hyperbolic",
    max_tokens: 131_000,
    id: "hyperbolic",
  },
  together: {
    name: "Together AI",
    max_tokens: 128_000,
    id: "together",
  },
  groq: {
    name: "Groq",
    max_tokens: 32_000,
    id: "groq",
    requiresApiKey: true,
    apiKeyName: "GROQ_API_KEY",
  },
  openrouter: {
    name: "OpenRouter",
    max_tokens: 128_000,
    id: "openrouter",
    requiresApiKey: true,
    apiKeyName: "OPENROUTER_API_KEY",
  },
  google: {
    name: "Google AI",
    max_tokens: 32_000,
    id: "google",
    requiresApiKey: true,
    apiKeyName: "GOOGLE_API_KEY",
  },
};

export const MODELS = [
  {
    value: "deepseek-ai/DeepSeek-V3-0324",
    label: "DeepSeek V3 O324",
    providers: ["fireworks-ai", "nebius", "sambanova", "novita", "hyperbolic"],
    autoProvider: "fireworks-ai",
  },
  {
    value: "deepseek-ai/DeepSeek-R1-0528",
    label: "DeepSeek R1 0528",
    providers: [
      "fireworks-ai",
      "novita",
      "hyperbolic",
      "nebius",
      "together",
      "sambanova",
    ],
    autoProvider: "novita",
    isNew: true,
    isThinker: true,
  },
  // Groq Models
  {
    value: "llama-3.1-70b-versatile",
    label: "Llama 3.1 70B Versatile",
    providers: ["groq"],
    autoProvider: "groq",
  },
  {
    value: "llama-3.1-8b-instant",
    label: "Llama 3.1 8B Instant",
    providers: ["groq"],
    autoProvider: "groq",
  },
  {
    value: "mixtral-8x7b-32768",
    label: "Mixtral 8x7B",
    providers: ["groq"],
    autoProvider: "groq",
  },
  // OpenRouter Models
  {
    value: "anthropic/claude-3.5-sonnet",
    label: "Claude 3.5 Sonnet",
    providers: ["openrouter"],
    autoProvider: "openrouter",
  },
  {
    value: "openai/gpt-4o",
    label: "GPT-4o",
    providers: ["openrouter"],
    autoProvider: "openrouter",
  },
  {
    value: "meta-llama/llama-3.1-405b-instruct",
    label: "Llama 3.1 405B Instruct",
    providers: ["openrouter"],
    autoProvider: "openrouter",
  },
  // Google Models
  {
    value: "gemini-1.5-pro",
    label: "Gemini 1.5 Pro",
    providers: ["google"],
    autoProvider: "google",
  },
  {
    value: "gemini-1.5-flash",
    label: "Gemini 1.5 Flash",
    providers: ["google"],
    autoProvider: "google",
  },
  {
    value: "gemini-1.0-pro",
    label: "Gemini 1.0 Pro",
    providers: ["google"],
    autoProvider: "google",
  },
];
