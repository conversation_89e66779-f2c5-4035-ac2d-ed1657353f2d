import { useState, useEffect } from "react";
import { <PERSON>, EyeOff, Key, Check, X } from "lucide-react";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { toast } from "sonner";
import ApiKeyManager, { ApiKeys } from "../../utils/api-keys";
import { PROVIDERS } from "../../../utils/providers";

interface ApiKeyConfigProps {
  provider: keyof ApiKeys;
  onKeyChange?: (hasKey: boolean) => void;
}

export function ApiKeyConfig({ provider, onKeyChange }: ApiKeyConfigProps) {
  const [apiKey, setApiKey] = useState("");
  const [showKey, setShowKey] = useState(false);
  const [isValid, setIsValid] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const providerInfo = PROVIDERS[provider];

  useEffect(() => {
    // Load existing API key
    const existingKey = ApiKeyManager.getApiKey(provider);
    if (existingKey) {
      setApiKey(existingKey);
      setIsValid(true);
    }
  }, [provider]);

  useEffect(() => {
    // Validate API key format
    const valid = ApiKeyManager.validateApiKey(provider, apiKey);
    setIsValid(valid);
    onKeyChange?.(valid);
  }, [apiKey, provider, onKeyChange]);

  const handleSave = async () => {
    if (!apiKey.trim()) {
      toast.error("Please enter an API key");
      return;
    }

    if (!isValid) {
      toast.error("Invalid API key format");
      return;
    }

    setIsSaving(true);
    try {
      ApiKeyManager.setApiKey(provider, apiKey);
      toast.success(`${providerInfo.name} API key saved successfully`);
    } catch (error) {
      toast.error("Failed to save API key");
    } finally {
      setIsSaving(false);
    }
  };

  const handleRemove = () => {
    ApiKeyManager.removeApiKey(provider);
    setApiKey("");
    setIsValid(false);
    toast.success(`${providerInfo.name} API key removed`);
  };

  const getPlaceholder = () => {
    switch (provider) {
      case 'groq':
        return "gsk_...";
      case 'openrouter':
        return "sk-or-...";
      case 'google':
        return "AIza...";
      default:
        return "Enter API key";
    }
  };

  const getInstructions = () => {
    switch (provider) {
      case 'groq':
        return "Get your API key from console.groq.com";
      case 'openrouter':
        return "Get your API key from openrouter.ai/keys";
      case 'google':
        return "Get your API key from console.cloud.google.com";
      default:
        return "";
    }
  };

  return (
    <div className="space-y-3">
      <div className="flex items-center gap-2">
        <Key className="size-4 text-neutral-400" />
        <span className="text-sm font-medium text-neutral-300">
          {providerInfo.name} API Key
        </span>
        {isValid && <Check className="size-4 text-green-500" />}
      </div>
      
      <div className="relative">
        <Input
          type={showKey ? "text" : "password"}
          value={apiKey}
          onChange={(e) => setApiKey(e.target.value)}
          placeholder={getPlaceholder()}
          className="pr-10"
        />
        <button
          type="button"
          onClick={() => setShowKey(!showKey)}
          className="absolute right-3 top-1/2 -translate-y-1/2 text-neutral-400 hover:text-neutral-300"
        >
          {showKey ? <EyeOff className="size-4" /> : <Eye className="size-4" />}
        </button>
      </div>

      <p className="text-xs text-neutral-400">
        {getInstructions()}
      </p>

      <div className="flex gap-2">
        <Button
          size="sm"
          onClick={handleSave}
          disabled={!apiKey.trim() || !isValid || isSaving}
          className="flex-1"
        >
          {isSaving ? "Saving..." : "Save"}
        </Button>
        {isValid && (
          <Button
            size="sm"
            variant="secondary"
            onClick={handleRemove}
          >
            <X className="size-4" />
          </Button>
        )}
      </div>
    </div>
  );
}
