/* eslint-disable @typescript-eslint/no-explicit-any */
import classNames from "classnames";
import { PiGearSixFill } from "react-icons/pi";
import { RiCheckboxCircleFill } from "react-icons/ri";
import { useState, useMemo } from "react";
import { useUpdateEffect } from "react-use";

import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
// @ts-expect-error not needed
import { PROVIDERS, MODELS } from "./../../../utils/providers";
import { Button } from "../ui/button";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { ApiKeyConfig } from "../api-key-config/api-key-config";
import ApiKeyManager from "../../utils/api-keys";

function Settings({
  open,
  onClose,
  provider,
  model,
  error,
  onChange,
  onModelChange,
}: {
  open: boolean;
  provider: string;
  model: string;
  error?: string;
  onClose: React.Dispatch<React.SetStateAction<boolean>>;
  onChange: (provider: string) => void;
  onModelChange: (model: string) => void;
}) {
  const [apiKeyStates, setApiKeyStates] = useState({
    groq: false,
    openrouter: false,
    google: false,
  });

  const modelAvailableProviders = useMemo(() => {
    const availableProviders = MODELS.find(
      (m: { value: string }) => m.value === model
    )?.providers;
    if (!availableProviders) return Object.keys(PROVIDERS);
    return Object.keys(PROVIDERS).filter((id) =>
      availableProviders.includes(id)
    );
  }, [model]);

  // Filter providers based on API key availability
  const availableProviders = useMemo(() => {
    return modelAvailableProviders.filter((id) => {
      const providerConfig = PROVIDERS[id];
      if (!providerConfig.requiresApiKey) return true;
      return ApiKeyManager.hasApiKey(id as keyof typeof apiKeyStates);
    });
  }, [modelAvailableProviders, apiKeyStates]);

  useUpdateEffect(() => {
    if (provider !== "auto" && !availableProviders.includes(provider)) {
      onChange("auto");
    }
  }, [model, provider, availableProviders]);

  const handleApiKeyChange = (providerKey: keyof typeof apiKeyStates, hasKey: boolean) => {
    setApiKeyStates(prev => ({ ...prev, [providerKey]: hasKey }));
  };

  return (
    <div className="">
      <Popover open={open} onOpenChange={onClose}>
        <PopoverTrigger asChild>
          <Button variant="gray" size="sm">
            <PiGearSixFill className="size-4" />
            Settings
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="!rounded-2xl p-0 !w-96 overflow-hidden !bg-neutral-900 max-h-[80vh] overflow-y-auto"
          align="center"
        >
          <header className="flex items-center text-sm px-4 py-3 border-b gap-2 bg-neutral-950 border-neutral-800 font-semibold text-neutral-200">
            {/* <span className="text-xs bg-blue-500 text-white rounded-full px-1.5 py-0.5">
              Provider
            </span> */}
            Customize Settings
          </header>
          <main className="px-4 pt-5 pb-6 space-y-5">
            <a
              href="https://huggingface.co/spaces/enzostvs/deepsite/discussions/74"
              target="_blank"
              className="w-full flex items-center justify-between text-neutral-300 bg-neutral-300/15 border border-neutral-300/15 pl-4 p-1.5 rounded-full text-sm font-medium hover:brightness-95"
            >
              How to use it locally?
              <Button size="xs">See guide</Button>
            </a>
            {error !== "" && (
              <p className="text-red-500 text-sm font-medium mb-2 flex items-center justify-between bg-red-500/10 p-2 rounded-md">
                {error}
              </p>
            )}
            <label className="block">
              <p className="text-neutral-300 text-sm mb-2.5">
                Choose an AI model
              </p>
              <Select defaultValue={model} onValueChange={onModelChange}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select an AI model" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>DeepSeek models</SelectLabel>
                    {MODELS.filter((m: any) => m.providers.some((p: any) => ["fireworks-ai", "nebius", "sambanova", "novita", "hyperbolic", "together"].includes(p))).map(
                      ({
                        value,
                        label,
                        isNew = false,
                      }: {
                        value: string;
                        label: string;
                        isNew?: boolean;
                      }) => (
                        <SelectItem key={value} value={value} className="">
                          {label}
                          {isNew && (
                            <span className="text-xs bg-gradient-to-br from-sky-400 to-sky-600 text-white rounded-full px-1.5 py-0.5">
                              New
                            </span>
                          )}
                        </SelectItem>
                      )
                    )}
                  </SelectGroup>
                  <SelectGroup>
                    <SelectLabel>Groq models</SelectLabel>
                    {MODELS.filter((m: any) => m.providers.includes("groq")).map(
                      ({
                        value,
                        label,
                      }: {
                        value: string;
                        label: string;
                      }) => (
                        <SelectItem key={value} value={value} className="">
                          {label}
                        </SelectItem>
                      )
                    )}
                  </SelectGroup>
                  <SelectGroup>
                    <SelectLabel>OpenRouter models</SelectLabel>
                    {MODELS.filter((m: any) => m.providers.includes("openrouter")).map(
                      ({
                        value,
                        label,
                      }: {
                        value: string;
                        label: string;
                      }) => (
                        <SelectItem key={value} value={value} className="">
                          {label}
                        </SelectItem>
                      )
                    )}
                  </SelectGroup>
                  <SelectGroup>
                    <SelectLabel>Google models</SelectLabel>
                    {MODELS.filter((m: any) => m.providers.includes("google")).map(
                      ({
                        value,
                        label,
                      }: {
                        value: string;
                        label: string;
                      }) => (
                        <SelectItem key={value} value={value} className="">
                          {label}
                        </SelectItem>
                      )
                    )}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </label>
            <div className="flex flex-col gap-3">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-neutral-300 text-sm mb-1.5">
                    Use auto-provider
                  </p>
                  <p className="text-xs text-neutral-400/70">
                    We'll automatically select the best provider for you based
                    on your prompt.
                  </p>
                </div>
                <div
                  className={classNames(
                    "bg-neutral-700 rounded-full min-w-10 w-10 h-6 flex items-center justify-between p-1 cursor-pointer transition-all duration-200",
                    {
                      "!bg-sky-500": provider === "auto",
                    }
                  )}
                  onClick={() => {
                    const foundModel = MODELS.find(
                      (m: { value: string }) => m.value === model
                    );
                    if (provider === "auto") {
                      onChange(foundModel.autoProvider);
                    } else {
                      onChange("auto");
                    }
                  }}
                >
                  <div
                    className={classNames(
                      "w-4 h-4 rounded-full shadow-md transition-all duration-200 bg-neutral-200",
                      {
                        "translate-x-4": provider === "auto",
                      }
                    )}
                  />
                </div>
              </div>
              <label className="block">
                <p className="text-neutral-300 text-sm mb-2">
                  Inference Provider
                </p>
                <div className="grid grid-cols-2 gap-1.5">
                  {availableProviders.map((id: string) => (
                    <Button
                      key={id}
                      variant={id === provider ? "default" : "secondary"}
                      size="sm"
                      onClick={() => {
                        onChange(id);
                      }}
                    >
                      <img
                        src={`/providers/${id}.svg`}
                        alt={PROVIDERS[id].name}
                        className="size-5 mr-2"
                      />
                      {PROVIDERS[id].name}
                      {id === provider && (
                        <RiCheckboxCircleFill className="ml-2 size-4 text-blue-500" />
                      )}
                    </Button>
                  ))}
                </div>
              </label>
            </div>

            {/* API Key Configuration Section */}
            <div className="space-y-4 border-t border-neutral-800 pt-5">
              <h3 className="text-neutral-300 text-sm font-medium">API Key Configuration</h3>
              <p className="text-xs text-neutral-400">
                Configure API keys for external providers to unlock additional models.
              </p>

              <div className="space-y-4">
                <ApiKeyConfig
                  provider="groq"
                  onKeyChange={(hasKey) => handleApiKeyChange('groq', hasKey)}
                />
                <ApiKeyConfig
                  provider="openrouter"
                  onKeyChange={(hasKey) => handleApiKeyChange('openrouter', hasKey)}
                />
                <ApiKeyConfig
                  provider="google"
                  onKeyChange={(hasKey) => handleApiKeyChange('google', hasKey)}
                />
              </div>
            </div>
          </main>
        </PopoverContent>
      </Popover>
    </div>
  );
}
export default Settings;
