// API Key management utility
const API_KEYS_STORAGE_KEY = "deepsite_api_keys";

export interface ApiKeys {
  groq?: string;
  openrouter?: string;
  google?: string;
}

export class ApiKeyManager {
  private static instance: ApiKeyManager;
  
  static getInstance(): ApiKeyManager {
    if (!ApiKeyManager.instance) {
      ApiKeyManager.instance = new ApiKeyManager();
    }
    return ApiKeyManager.instance;
  }

  private constructor() {}

  // Get all API keys from localStorage
  getApiKeys(): ApiKeys {
    try {
      const stored = localStorage.getItem(API_KEYS_STORAGE_KEY);
      if (!stored) return {};
      return JSON.parse(stored);
    } catch (error) {
      console.error("Error loading API keys:", error);
      return {};
    }
  }

  // Save API keys to localStorage
  private saveApiKeys(keys: ApiKeys): void {
    try {
      localStorage.setItem(API_KEYS_STORAGE_KEY, JSON.stringify(keys));
    } catch (error) {
      console.error("Error saving API keys:", error);
      throw new Error("Failed to save API keys");
    }
  }

  // Get a specific API key
  getApiKey(provider: keyof ApiKeys): string | undefined {
    const keys = this.getApiKeys();
    return keys[provider];
  }

  // Set a specific API key
  setApiKey(provider: keyof ApiKeys, key: string): void {
    const keys = this.getApiKeys();
    if (key.trim()) {
      keys[provider] = key.trim();
    } else {
      delete keys[provider];
    }
    this.saveApiKeys(keys);
  }

  // Remove a specific API key
  removeApiKey(provider: keyof ApiKeys): void {
    const keys = this.getApiKeys();
    delete keys[provider];
    this.saveApiKeys(keys);
  }

  // Check if a provider has an API key
  hasApiKey(provider: keyof ApiKeys): boolean {
    const key = this.getApiKey(provider);
    return !!key && key.length > 0;
  }

  // Validate API key format (basic validation)
  validateApiKey(provider: keyof ApiKeys, key: string): boolean {
    if (!key || key.trim().length === 0) return false;
    
    switch (provider) {
      case 'groq':
        return key.startsWith('gsk_');
      case 'openrouter':
        return key.startsWith('sk-or-');
      case 'google':
        return key.length > 10; // Basic length check for Google API keys
      default:
        return false;
    }
  }

  // Clear all API keys
  clearAllApiKeys(): void {
    localStorage.removeItem(API_KEYS_STORAGE_KEY);
  }
}

export default ApiKeyManager.getInstance();
